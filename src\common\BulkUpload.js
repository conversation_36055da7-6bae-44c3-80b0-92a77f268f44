import React, { useState } from "react";
import { UploadIcon, EditingIcon } from "../icons";
import BulkUploadDialog from "./BulkUploadDialog";

const BulkUpload = (props) => {
  // const [file, setFile] = useState(null);
  const [showDialog, setShowDialog] = useState(false);
  const [file, setFile] = useState(null);
  // const fileInputRef = useRef(null);

  // const handleFileChange = (event) => {
  //   const selectedFile = event.target.files[0];
  //   setFile(selectedFile);
  //   if (selectedFile) {
  //     setShowDialog(true); // open dialog only after selecting a file
  //   }
  // };

  const handleUploadClick = () => {
    setShowDialog(true);
    // fileInputRef.current.click(); // trigger hidden input
  };

  return (
    <>
      <div
        className="min-w-[164px] min-h-[31px] flex items-center justify-center border border-gray-300 rounded-md cursor-pointer gap-2 mt-8"
        onClick={handleUploadClick}
      >
        {file ? <EditingIcon /> : <UploadIcon />}
        <div className="max-w-xs">
          <span className="text-black text-sm break-words whitespace-normal">
            {file ? `File "${file.filename}" Uploaded` : "Upload Email List"}
          </span>
        </div>
      </div>

      {showDialog && (
        <BulkUploadDialog
          show={showDialog}
          setFile={setFile}
          file={file}
          onClose={() => setShowDialog(false)}
          setGroups={props?.setGroups}
        />
      )}
    </>
  );
};

export default BulkUpload;
