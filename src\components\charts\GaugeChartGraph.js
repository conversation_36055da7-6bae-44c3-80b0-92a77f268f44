import React, { useState, useMemo, useEffect, useCallback } from "react";
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer } from "recharts";
import { Formik, Form } from "formik";
import Select from "../FormsUI/Select";
import Button from "../Button/OutlinedButton";

const COLORS = ["#83B776", "#DC9033"]; // Green for actual, Orange for expected remainder

const GaugeChartGraph = ({ data = [], config = {} }) => {
  const [chartData, setChartData] = useState(null);

  const {
    xAxis: selectorField,
    yAxis: yAxisFields,
    configExpectedValue: expectedValue,
  } = config;

  const dateOptions = useMemo(
    () =>
      selectorField && data.length > 0
        ? data.map((d) => ({
            value: d[selectorField],
            label: d[selectorField],
          }))
        : [],
    [data, selectorField]
  );

  const handleSubmit = useCallback(
    (values) => {
      if (!values.selectedDate || !yAxisFields) return;

      const selectedData = data.find(
        (d) => d[selectorField] === values.selectedDate
      );

      if (selectedData) {
        const actualValue = selectedData[yAxisFields];
        const percentage =
          expectedValue > 0 ? (actualValue / expectedValue) * 100 : 0;
        setChartData({
          gaugeData: [
            { name: "Actual", value: actualValue },
            {
              name: "Expected",
              value: expectedValue,
            },
          ],
          actualValue,
          expectedValue,
          percentage,
        });
      }
    },
    [data, selectorField, yAxisFields, expectedValue]
  );

  useEffect(() => {
    if (dateOptions.length > 0)
      handleSubmit({ selectedDate: dateOptions[0].value });
  }, [dateOptions, handleSubmit]);

  if (!data || data.length === 0)
    return <div className="text-center text-gray-500">No data available</div>;
  if (!selectorField || !yAxisFields)
    return (
      <div className="text-center text-red-500">
        Chart configuration is incomplete.
      </div>
    );

  return (
    <>
      <div className="w-full max-w-md ">
        <Formik
          initialValues={{ selectedDate: dateOptions[0]?.value || "" }}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values }) => (
            <Form className="flex items-end gap-4 mb-4 w-10/12">
              <div className="flex-grow mx-4 ">
                <label
                  htmlFor="selectedDate"
                  className="block text-sm font-medium text-gray-700 text-left mb-2"
                >
                  Select a {selectorField}
                </label>
                <Select
                  id="selectedDate"
                  name="selectedDate"
                  options={dateOptions}
                  placeholder={`Select a ${selectorField}`}
                  onChange={(selectedOption) => {
                    handleSubmit({ selectedDate: selectedOption.value });
                  }}
                />
              </div>
              {/* <Button label="Apply" type="submit" /> */}
            </Form>
          )}
        </Formik>
      </div>
      {chartData && (
        <div className="flex flex-col items-center">
          <ResponsiveContainer width="100%" height={300}>
            <PieChart margin={{ top: 40 }}>
              <Pie
                data={chartData.gaugeData}
                startAngle={180}
                endAngle={0}
                innerRadius={"85%"}
                outerRadius={"130%"}
                dataKey="value"
              >
                {chartData.gaugeData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={COLORS[index % COLORS.length]}
                  />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>

          {/* Percentage + values in center */}
          <div className="text-center -mt-48">
            <p className="text-xl text-black mb-3">
              {chartData.percentage.toFixed(2)}%
            </p>
            <p>
              <span className="text-[#83B776] ">{chartData.actualValue}</span>
              <span className="text-black"> / </span>
              <span className="text-[#DC9033] ">{chartData.expectedValue}</span>
            </p>
          </div>
        </div>
      )}
      {/* Legend */}
      <div className="flex flex-col gap-3 mt-4 mx-8">
        <div className="flex items-center  gap-2">
          <span
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: COLORS[0] }}
          ></span>
          <span className="text-sm text-[#83B776]">{config.xAxis}</span>
        </div>
        <div className="flex items-center  gap-2">
          <span
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: COLORS[1] }}
          ></span>
          <span className="text-sm text-[#DC9033]">Expected</span>
        </div>
      </div>
    </>
  );
};

export default GaugeChartGraph;
