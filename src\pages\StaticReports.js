import React, {
  useMemo,
  useState,
  useEffect,
  useContext,
  useRef,
  useCallback,
} from "react";
import Pagination from "../components/Pagination/Pagination";
import { reportService } from "../services/staticreport.service";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import ExportPopup from "../popups/exportpopup";
import {
  MailBoxIcon,
  CloseIcon,
  SearchhIcon,
  InfoIcon,
  RefreshIcon,
  MenusIcon,
  FileRefreshIcon,
  ChartIcon,
  TableChartIcon,
  SavePrefernceIcon,
} from "../icons";
import { AuthContext } from "../context/AuthContext";
import InfoModal from "../components/modals/InfoModal";
import { DataContext } from "../context/DataContext";
import ResultPerPageComponent from "../components/Pagination/ResultsPerPage";
import { useNavigate } from "react-router-dom";
import { useLocation } from "react-router-dom";
import Button from "../components/Button/OutlinedButton";
import OutlinedButton from "../components/Button/Button";
import SendMail from "../popups/SendMail";
import { CssTooltip } from "../components/StyledComponent";
import bgImage from "../assets/img/Records.png";
import { dynamicReports, views } from "../common/constants";
import ErrorDialog from "../popups/ErrorDialog";
import BreadcrumbNavigation from "../components/BreadCrumps/BreadCrump";
import { DownloadContext } from "../context/DownloadContext";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import theme from "../tailwind-theme";
import ReportTable from "../components/table/ReportTable";
import AggregationTable from "../components/table/AggregationTable";
import ReportFilter from "../components/CollapsibleFilter/ReportFilter";
import { MetaDataProvider } from "../context/MetaDataContext";
import SuccessDialog from "../popups/SuccessDialog";
import ReportCalendar from "../components/DatePicker/ReportCalendar";
import { NO_FILTER } from "../utils/constants";
import { billingReportRefresh } from "../services/cdrsearch-api";
import { useMutation, useQuery } from "react-query";
import {
  getExportPermissions,
  createExportTooltipContent,
} from "../utils/exportUtils";
import {
  generateReportFilename,
  getInitialFilterAndRange,
} from "../utils/dateRangeUtils";
import { REPORT_INFO_TOOLTIP } from "../common/constants";
import { getTimeRangeOptions } from "../utils/reportUtils";
import ColumnSelector from "../components/ColumnSelector/ColumnSelector";
import {
  getSelectedColumn,
  reportSave,
} from "../services/staticreport.service";
import graphImage from "../assets/img/graph.png";
import LoadingOverlay from "../components/LoadingOverlay";
import LineChartGraph from "../components/charts/LineChartGraph";
import BarChartGraph from "../components/charts/BarChartGraph";
import PieChartComponent from "../components/charts/PieChartGraph";
import { generatePDFFromElement } from "../utils/pdfUtils";
import { exportDataToCSV } from "../utils/csvExportUtils";
import GaugeChartGraph from "../components/charts/GaugeChartGraph";
import ScatterPlotChartGraph from "../components/charts/ScatterPlotChartGraph";
import HeatMapChartGraph from "../components/charts/HeatMapChartGraph";
import MultiAxisChartGraph from "../components/charts/MultiAxisChartGraph";

function StaticReports({ onClose }) {
  const location = useLocation();
  const { value: data, viewBy, activeTab, subtype } = location.state || {};
  const { resultPerPage } = useContext(DataContext);
  const [limitPerPage, setLimitPerPage] = useState(100);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState("");
  const [details, setDetails] = useState([]);
  const [filters, setFilters] = useState([]);
  const [graphFilters, setgraphFilters] = useState([]);
  const [aggrgationDetail, setAggrgationDetail] = useState([]);
  const [showExportConfirmation, setShowExportConfirmation] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isDowloadLoading, setIsDownloadLoading] = useState(false);
  const [labelData, setLabelData] = useState([]);
  const [selectedFilter, setSelectedFilter] = useState(() => {
    const initial = getInitialFilterAndRange(data, dynamicReports);
    return initial.filter;
  });
  const [selectedRange, setSelectedRange] = useState(() => {
    const initial = getInitialFilterAndRange(data, dynamicReports);
    return initial.range;
  });
  const [searchStr, setSearchStr] = useState("");
  const [reload, setReload] = useState(false);
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  const [sendMailDialog, setSendMailDialog] = useState(false);
  const [filterDialog, setFilterDialog] = useState(false);
  const [graphFilterDialog, setGraphFilterDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [extensionType, setExtensionType] = useState("");
  const [errorDialog, setErrorDialog] = useState(false);
  const [refreshClick, setRefreshClick] = useState(false);
  const [successDialog, setSuccessDialog] = useState(false);
  const [showMenuDropdown, setShowMenuDropdown] = useState(false);
  const [showFtpOptions, setShowFtpOptions] = useState(false);
  const [ftpRefreshType, setFtpRefreshType] = useState("");
  const [openDialog, setOpenDialog] = useState(false);
  const [allTableColumns, setAllTableColumns] = useState([]);
  const [visibleColumns, setVisibleColumns] = useState({});
  const [derivedFields, setDerivedFields] = useState([]);
  const [activeView, setActiveView] = useState("table");
  const [showGraph, setShowGraph] = useState(false);
  const [payload, setPayload] = useState([]);

  const menuRef = useRef(null);
  const searchTimeoutRef = useRef(null);
  const graphContainerRef = useRef(null);

  const navigate = useNavigate();

  dayjs.extend(customParseFormat);

  const { isDownloading, setIsDownloading } = useContext(DownloadContext);
  const { roles, configApiData, user } = useContext(AuthContext);

  const { mutate: staticReportAPI, isLoading: loadingData } = useMutation(
    reportService.getReport
  );
  const { mutate: reportSaveAPI, isLoading: saveloading } =
    useMutation(reportSave);

  const matchingReports = roles?.staticReports?.filter((report) => {
    return report.name === data;
  });
  const permission = user.isSuperAdmin
    ? 1
    : matchingReports[0]?.permissions?.download;

  const isAdmin = user.isSuperAdmin;

  function handlePageChange(page) {
    setCurrentPage(page);
  }

  const getUrl = new URL(window.location.href);
  const timeZone = getUrl.search.slice(1);

  const exportPermissions = getExportPermissions(totalCount, configApiData);

  const handleLimitChange = (e) => {
    setCurrentPage(1);
    setLimitPerPage(e?.target?.value);
    if (e.target.value > 1000) {
      toast.warn("There might be delay in loading larger data.");
    }
  };

  const handleKeyUp = useCallback((event) => {
    const value = event.target.value;
    setSearchStr(value);
    setCurrentPage(1);

    // Clear existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set new timeout for debouncing
    searchTimeoutRef.current = setTimeout(() => {
      setReload((prev) => !prev); // Toggle reload to trigger useEffect
    }, 500); // 500ms debounce delay
  }, []);
  const staticReport = useCallback(() => {
    // Only call API for table view, or for graph view if graphFilters is present
    if (
      activeView === "table" ||
      (activeView === "graph" &&
        graphFilters &&
        Object.keys(graphFilters).length > 0)
    ) {
      const payload = {
        reportName: data,
        download: 0,
        type: "",
        limit:
          activeView === "table"
            ? limitPerPage
            : configApiData?.GRAPH_PAGE_LIMIT,
        page: currentPage,
        search: searchStr,
        timezone: timeZone,
      };
      if (!columnConfigData?.data?.noStartEndDate) {
        payload.startDate = selectedFilter.startDate;
        payload.endDate = selectedFilter.endDate;
      }
      if (["Daily", "Weekly", "Monthly"].includes(selectedFilter.duration)) {
        payload.defaultViewBy =
          selectedFilter.duration === "Daily"
            ? "day"
            : selectedFilter.duration === "Weekly"
            ? "week"
            : "month";
      }
      if (filters && filters.length !== 0) {
        payload.filters = filters;
      }
      if (activeView === "graph") {
        payload.graphFilters = graphFilters;
        payload.isGraph = true;
      }
      if (activeView === "table") {
        payload.isGraph = false;
      }
      setPayload(payload);
      staticReportAPI(
        { payload },
        {
          onSuccess: (res) => {
            let newData = res?.data?.data?.map((x) => {
              const newObj = { ...x };
              return newObj;
            });
            setDetails(newData);
            setAggrgationDetail(res.data.aggregateResponse);
            setTotalCount(res.data.totalCount);
            if (
              res.data.totalCount > configApiData?.GRAPH_PAGE_LIMIT &&
              activeView === "graph"
            ) {
              setShowAlertConfirmation(true);
              setMessage(
                "Reframe from using huge data to be displayed in the graph.Selecting these many will result in a cluttered and unreadable graph. Please select optimal data."
              );
              setShowGraph(false);
            } else {
              setShowGraph(true);
            }
          },
          onError: (error) => {
            setDetails([]);
            setAggrgationDetail([]);
            setTotalCount(0);
          },
        }
      );
    }
  }, [
    activeView,
    graphFilters,
    data,
    limitPerPage,
    configApiData?.GRAPH_PAGE_LIMIT,
    currentPage,
    searchStr,
    timeZone,
    selectedFilter.startDate,
    selectedFilter.endDate,
    selectedFilter.duration,
    filters,
    staticReportAPI,
  ]);
  useEffect(() => {
    staticReport();
  }, [
    currentPage,
    limitPerPage,
    selectedFilter,
    reload,
    refreshClick,
    filters,
    staticReport,
  ]);

  useEffect(() => {
    setCurrentPage(1);
  }, [selectedFilter]);

  // Cleanup search timeout on component unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  const exportReport = (type) => {
    setIsLoading(true);
    setIsDownloadLoading(true);

    const fileName =
      generateReportFilename(
        data,
        selectedFilter.startDate,
        selectedFilter.endDate
      ) + (filters && filters.length !== 0 ? "_filters" : "");
    setExtensionType(type);

    if (totalCount < configApiData.INITIATE_OFFLINE_DOWNLOAD) {
      setIsDownloading(true);
    }

    if (type !== "") {
      reportService
        .downloadReport(
          data,
          "1",
          type,
          !columnConfigData?.data.noStartEndDate
            ? selectedFilter.startDate
            : undefined,
          !columnConfigData?.data.noStartEndDate
            ? selectedFilter.endDate
            : undefined,
          searchStr,
          timeZone,
          totalCount,
          filters,
          selectedFilter.duration,
          configApiData.INITIATE_OFFLINE_DOWNLOAD,
          fileName
        )
        .then((blob) => {
          setIsLoading(false);
          if (totalCount > configApiData.INITIATE_OFFLINE_DOWNLOAD) {
            setSuccessDialog(true);
            setMessage(blob.data.message);
          } else {
            const url = URL.createObjectURL(blob.data);
            const link = document.createElement("a");
            const filename = fileName + ".zip";
            link.href = url;
            link.download = filename;
            link.click();
          }
        })
        .catch((error) => {
          setIsDownloadLoading(false);
          setErrorDialog(true);
          setMessage("No record found");
        })
        .finally(() => {
          setIsDownloading(false);
          setIsDownloadLoading(false);
        });
    }
  };

  // Separate function for graph download as PDF
  const exportGraphAsPDF = () => {
    if (!graphContainerRef.current) {
      setErrorDialog(true);
      setMessage("Graph not available for download");
      return;
    }

    // Check if there's data for the graph
    if (!details || details.length === 0) {
      setErrorDialog(true);
      setMessage("No data available for graph download");
      return;
    }

    setIsDownloadLoading(true);
    setIsDownloading(true);

    const fileName =
      generateReportFilename(
        data,
        selectedFilter.startDate,
        selectedFilter.endDate
      ) +
      (filters && filters.length !== 0 ? "_filters" : "") +
      "_graph";

    // Use utility function for PDF generation
    generatePDFFromElement(
      graphContainerRef.current,
      fileName,
      `${data} - Graph Report`,
      () => {
        setIsDownloadLoading(false);
        setIsDownloading(false);
      },
      (error) => {
        setErrorDialog(true);
        setMessage("Error generating PDF. Please try again.");
        setIsDownloadLoading(false);
        setIsDownloading(false);
      }
    );
  };

  // New function for graph data download as CSV
  const exportGraphDataAsCSV = () => {
    if (!details || details.length === 0) {
      setErrorDialog(true);
      setMessage("No data available for CSV download");
      return;
    }

    const fileName =
      generateReportFilename(
        data,
        selectedFilter.startDate,
        selectedFilter.endDate
      ) +
      (filters && filters.length !== 0 ? "_filters" : "") +
      "_graph_data";

    exportDataToCSV({
      data: details,
      filename: fileName,
      options: {
        title: `Reports for ${selectedFilter.startDate} to ${selectedFilter.endDate}`,
      },
      onStart: () => {
        setIsDownloadLoading(true);
        setIsDownloading(true);
      },
      onSuccess: () => {
        setIsDownloadLoading(false);
        setIsDownloading(false);
      },
      onError: (errorMessage) => {
        setErrorDialog(true);
        setMessage(errorMessage || "Error generating CSV. Please try again.");
        setIsDownloadLoading(false);
        setIsDownloading(false);
      },
    });
  };

  // Fetch columns for ColumnSelector (only for Table Reports)
  const { data: columnConfigData, refetch } = useQuery(
    [
      "reportList",
      "static",
      data,
      // If you have a reportId, add it here (e.g., location.state?.id)
    ],
    getSelectedColumn,
    {
      enabled: !!data,
      onSuccess: ({ data }) => {
        const { filters = [], graphFilters = {} } = data?.graphPreference;
        if (activeView === "graph") {
          setFilters(filters);
          setgraphFilters(graphFilters);
        }
        const { dataColumns, selectedColumns = [] } = data || {};
        if (!dataColumns) return;
        const { tableFields = [], derivedFields = [] } = dataColumns;

        const combinedFields = [...tableFields, ...derivedFields];
        setDerivedFields(derivedFields);
        setAllTableColumns(combinedFields);
        if (Object.keys(visibleColumns).length > 0) return;
        const initialVisibility = combinedFields.reduce((acc, field) => {
          // Always show date columns
          if (field.toLowerCase().includes("date")) {
            acc[field] = true;
          } else {
            acc[field] =
              selectedColumns.length > 0
                ? selectedColumns.includes(field)
                : true;
          }
          return acc;
        }, {});
        setVisibleColumns(initialVisibility);
      },
    }
  );

  // Filter columns for table rendering
  const columns = useMemo(() => {
    if (!details || details.length === 0) return [];

    const firstItem = details[0];
    const keys = Object.keys(firstItem);

    const dynamicColumns = keys.map((key) => ({
      id: key,
      accessorKey: key,
      header: key,
      enableSorting: true,
      enableColumnFilter: true,
      cell: ({ row }) => row.original[key],
    }));

    return dynamicColumns;
  }, [details]);

  const filteredKeys = Object.keys(filters).filter(
    (key) => key !== "durationTime"
  );
  const badgeCount = filteredKeys.length;

  const removeFilter = NO_FILTER.some((filter) => filter === data);

  const tooltipContent = createExportTooltipContent(configApiData);

  const { mutate: ftpRefreshAPI } = useMutation(billingReportRefresh);

  const handleFtpRefresh = (type) => {
    let startDate, endDate;

    if (type === "This Month") {
      startDate = dayjs().startOf("month");
      endDate = dayjs();
    } else if (type === "Last Month") {
      const lastMonth = dayjs().subtract(1, "month");
      startDate = lastMonth.startOf("month");
      endDate = lastMonth.endOf("month");
    } else {
      return;
    }

    const reqObj = {
      reportName: data,
      timezone: timeZone,
      startDate: startDate.format("YYYY-MM-DD HH:mm:ss"),
      endDate: endDate.format("YYYY-MM-DD HH:mm:ss"),
      isRefresh: true,
    };
    ftpRefreshAPI(
      { reqObj },
      {
        onSuccess: ({ data }) => {
          setSuccessDialog(true);
          setMessage(data?.data?.message);
        },
        onError: () => {
          setErrorDialog(true);
          setMessage("Something went wrong ! Try Again");
        },
      }
    );

    setShowFtpOptions(false);
    setShowMenuDropdown(false);
  };

  useEffect(() => {
    function handleClickOutside(event) {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setShowMenuDropdown(false);
      }
    }

    if (showMenuDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showMenuDropdown]);

  const handleSaveGraph = () => {
    if (!details || details.length === 0) {
      setErrorDialog(true);
      setMessage("No data available to save");
      return;
    }
    setIsDownloadLoading(true);
    setIsDownloading(true);
    const {
      download,
      type,
      limit,
      page,
      search,
      timezone,
      startDate,
      endDate,
      ...rest
    } = payload;
    reportSaveAPI(
      {
        payload: {
          ...rest,
          reportType: "static",
        },
      },
      {
        onSuccess: () => {
          setIsDownloadLoading(false);
          setIsDownloading(false);
          setSuccessDialog(true);
          setMessage("Graph saved successfully");
        },
        onError: () => {
          setIsDownloadLoading(false);
          setIsDownloading(false);
          setErrorDialog(true);
          setMessage("Something went wrong ! Try Again");
        },
      }
    );
  };

  return (
    <>
      {/* Loading Overlay */}
      {loadingData && <LoadingOverlay message="Loading..." />}
      {/* Sticky Header with Breadcrumb */}
      <div className="sticky top-0 z-10 w-full bg-bgPrimary flex items-start text-headingColor text-2xl font-bold leading-tight">
        <BreadcrumbNavigation
          linkTwo="Static Reports"
          onlinkTwoClick={() =>
            navigate("/app/reports", {
              state: { tab: activeTab, subType: subtype },
            })
          }
          title={data}
        />
      </div>

      {/* Main Content Container */}
      <div className="bg-white p-3">
        {/* Close Icon Section */}
        <div className="flex justify-end items-center">
          <CloseIcon
            onClick={() =>
              navigate("/app/reports", {
                state: { tab: activeTab, subType: subtype },
              })
            }
            className="w-2 h-2 cursor-pointer mb-1"
          />
        </div>

        {/* Search and Controls */}

        <div className="mx-3 flex flex-wrap items-center justify-between gap-y-3">
          {/* Search Input */}
          {activeView === "table" ? (
            <div className="w-full md:w-[300px] relative">
              <input
                type="text"
                style={{
                  border: `1px solid ${theme.borderColor.outerBorder}`,
                  paddingLeft: "2.5rem",
                }}
                className="w-full text-tabColor bg-white rounded-md focus:outline-none text-sm h-10"
                placeholder="Search"
                value={searchStr}
                onChange={handleKeyUp}
              />
              <div className="absolute top-3 left-3">
                <SearchhIcon className="w-4 h-4" />
              </div>
            </div>
          ) : (
            <div className="w-full md:w-[300px] relative" />
          )}
          {/* Calendar, Info Tooltip, Mail, Filter, Refresh, Download */}
          <div className="flex items-center space-x-8">
            {/* Column Selector - Only show for Table Reports */}

            {!configApiData?.REPORTS_NOT_INCLUDES_EXPANDABLE_COLUMNS?.includes(
              data
            ) &&
              activeView === "table" && (
                <ColumnSelector
                  allFields={allTableColumns}
                  appliedFields={visibleColumns}
                  derivedFields={derivedFields}
                  onApply={(newVisibleColumns) => {
                    setVisibleColumns(newVisibleColumns);
                    setReload((prev) => !prev);
                  }}
                  onTriggerPreview={(columnSelection) => {
                    //  setSelectedColumn(columnSelection);
                  }}
                  reportName={data}
                  reportType={"static"}
                />
              )}

            {/* Menu Icon with Dropdown - Now positioned first */}
            <div className="relative" ref={menuRef}>
              <div
                className="rounded-full bg-bgouterBackground p-2 flex items-center justify-center cursor-pointer"
                onClick={() => {
                  setShowMenuDropdown(!showMenuDropdown);
                  setShowFtpOptions(false);
                  setOpenDialog(false);
                }}
              >
                <CssTooltip title={"Report Menu"} placement="top" arrow>
                  <MenusIcon className="w-5 h-5" />
                </CssTooltip>
              </div>

              {/* Dropdown Menu */}
              {showMenuDropdown && (
                <div className="absolute right-0 mt-2 w-72 bg-white rounded-md shadow-lg z-10 border border-gray-200 p-3">
                  <div className="grid grid-cols-2 gap-2">
                    {/* Send Mail Option */}
                    <div
                      className="flex items-center gap-1 cursor-pointer p-2 rounded hover:bg-gray-100"
                      onClick={() => {
                        setSendMailDialog(true);
                        setShowMenuDropdown(false);
                        setShowFtpOptions(false);
                      }}
                    >
                      <MailBoxIcon className="w-5 h-5" />
                      <span className="text-sm text-gray-700 ml-2 ">
                        Send Email
                      </span>
                    </div>
                    {/* Save Preference Option */}
                    {activeView === "graph" && details.length > 0 ? (
                      <div
                        className="flex items-center gap-1 cursor-pointer p-2 rounded hover:bg-gray-100"
                        onClick={() => {
                          handleSaveGraph();
                        }}
                      >
                        <SavePrefernceIcon className="w-6 h-6" />
                        <span className="text-sm text-gray-700 ml-1 whitespace-nowrap">
                          Save Graph
                        </span>
                      </div>
                    ) : null}

                    {/* Refresh Option (Only for Billing Tab) */}
                    {activeTab === 2 && subtype === "Billing" && (
                      <div
                        className="flex items-center gap-1 cursor-pointer p-2 rounded hover:bg-gray-100"
                        onClick={() => {
                          setRefreshClick(true);
                          setShowMenuDropdown(false);
                          setShowFtpOptions(false);
                          toast.success("Refresh Applied...");
                        }}
                      >
                        <RefreshIcon className="w-5 h-5" />
                        <span className="text-sm text-gray-700 ml-2 truncate">
                          Refresh
                        </span>
                      </div>
                    )}

                    {/* FTP Refresh Option with Submenu */}
                    {configApiData?.MONTHLY_BILLING_REPORTS.includes(data) &&
                      isAdmin && (
                        <>
                          {" "}
                          <div
                            className="flex items-center gap-1 cursor-pointer p-2 rounded hover:bg-gray-100"
                            onClick={() => setShowFtpOptions(!showFtpOptions)}
                          >
                            <FileRefreshIcon className="w-5 h-5" />
                            <span className="text-sm text-gray-700 ml-2 truncate">
                              FTP Refresh
                            </span>
                          </div>
                          {/* FTP Options Submenu with Radio Buttons */}
                          {showFtpOptions && (
                            <div className="absolute left-full top-16 bg-white rounded-md shadow-lg z-20 border border-gray-200 p-3 w-48">
                              <div className="text-sm font-medium mb-2">
                                Select Period:
                              </div>

                              <div className="flex items-center mb-2">
                                <input
                                  type="radio"
                                  id="thisMonth"
                                  name="ftpPeriod"
                                  value="This Month"
                                  checked={ftpRefreshType === "This Month"}
                                  onChange={() =>
                                    setFtpRefreshType("This Month")
                                  }
                                  className="mr-2 accent-black"
                                />
                                <label
                                  htmlFor="thisMonth"
                                  className="text-sm text-gray-700 cursor-pointer"
                                >
                                  This Month
                                </label>
                              </div>

                              <div className="flex items-center mb-3">
                                <input
                                  type="radio"
                                  id="lastMonth"
                                  name="ftpPeriod"
                                  value="Last Month"
                                  checked={ftpRefreshType === "Last Month"}
                                  onChange={() =>
                                    setFtpRefreshType("Last Month")
                                  }
                                  className="mr-2 accent-black"
                                />
                                <label
                                  htmlFor="lastMonth"
                                  className="text-sm text-gray-700 cursor-pointer"
                                >
                                  Last Month
                                </label>
                              </div>

                              <button
                                className="w-full py-1.5 bg-bgSecondary text-white text-sm rounded hover:bg-opacity-90"
                                onClick={() => {
                                  setShowFtpOptions(false);
                                  setShowMenuDropdown(false);
                                  handleFtpRefresh(ftpRefreshType);
                                }}
                              >
                                Apply
                              </button>
                            </div>
                          )}
                        </>
                      )}
                  </div>
                </div>
              )}
            </div>

            {/* Info Tooltip */}

            {/* Calendar Picker - Disabled for weekly/monthly */}

            {!columnConfigData?.data?.noStartEndDate ? (
              <>
                <CssTooltip
                  title={
                    <div className="text-xs p-1">
                      {REPORT_INFO_TOOLTIP.map((text, idx) => (
                        <p className="mb-1.5" key={idx}>
                          {text}
                        </p>
                      ))}
                    </div>
                  }
                  placement="top"
                  arrow
                >
                  <InfoIcon className="ml-2 mt-1 w-4 h-3.5" />
                </CssTooltip>
                <div
                  className={`${
                    filters?.duration === "weekly" ||
                    filters?.duration === "monthly"
                      ? "pointer-events-none opacity-50"
                      : ""
                  }`}
                  onClick={() => {
                    setShowMenuDropdown(false);
                  }}
                >
                  <ReportCalendar
                    selectedFilter={selectedFilter}
                    setSelectedFilter={setSelectedFilter}
                    setSelectedRange={setSelectedRange}
                    selectedRange={selectedRange}
                    reportTimeRange={getTimeRangeOptions(viewBy)}
                    viewBy={viewBy}
                    subtype={subtype}
                    openDialog={openDialog}
                    setOpenDialog={setOpenDialog}
                    data={data}
                    isAdmin={isAdmin}
                  />
                </div>
              </>
            ) : null}

            {/* Download Button */}
            <CssTooltip
              title={
                activeView === "table"
                  ? tooltipContent
                  : "Download graph as PDF or export underlying data as CSV. For large datasets, please apply filters for better performance."
              }
              placement="top"
              arrow
              PopperProps={{
                modifiers: [
                  {
                    name: "preventOverflow",
                    options: {
                      altBoundary: true,
                    },
                  },
                ],
              }}
              enterNextDelay={100}
              enterDelay={100}
              leaveDelay={200}
              componentsProps={{
                popper: {
                  sx: {
                    opacity: 1,
                  },
                },
              }}
            >
              <span style={{ display: "inline-block" }}>
                {" "}
                <Button
                  buttonClassName="text-xs w-32 text-white h-10 rounded-md"
                  label="Download"
                  onClick={() => {
                    if (permission === 0) {
                      setShowAlertConfirmation(true);
                      setMessage("Download permission not allowed");
                    } else {
                      if (activeView === "graph") {
                        // Check if there's data and graph configuration before allowing download
                        if (!details || details.length === 0) {
                          setShowAlertConfirmation(true);
                          setMessage("No data available for graph download");
                          return;
                        }
                        if (!graphFilters?.visualizationType) {
                          setShowAlertConfirmation(true);
                          setMessage(
                            "Please configure graph settings before downloading"
                          );
                          return;
                        }
                        // For graph view, show export options (PDF or CSV)
                        setShowExportConfirmation(true);
                      } else {
                        // For table view, show export options
                        setShowExportConfirmation(true);
                      }
                    }
                  }}
                  disabled={
                    isDownloading ||
                    isDowloadLoading ||
                    (activeView === "graph" &&
                      (!details ||
                        details.length === 0 ||
                        !graphFilters?.visualizationType))
                  }
                />
              </span>
            </CssTooltip>
          </div>
        </div>

        {/* Report Summary and Tables */}
        <div className="mx-3 mt-5">
          {/* Display Date Range if data exists */}
          {details.length > 0 && !columnConfigData?.data?.noStartEndDate && (
            <div className="mt-5 mx-1 flex items-center justify-between">
              <div className="text-sm text-black font-bold">
                Report from {selectedFilter?.startDate} to{" "}
                {selectedFilter?.endDate}
              </div>
            </div>
          )}
          {/* Aggregated Data Table */}
          {aggrgationDetail && Object.keys(aggrgationDetail).length > 0 && (
            <div className="mt-3">
              <AggregationTable aggregateResponse={aggrgationDetail} />
            </div>
          )}

          {/* ===== TABS START ===== */}

          <div className="mt-1 border-b border-gray-200 p-1">
            <nav className="-mb-px flex items-center" aria-label="Tabs">
              {/* Tabs */}
              {views.map((view, index) => (
                <button
                  key={view.value}
                  onClick={() => {
                    setActiveView(view.value);
                    // Reset tab state when switching views
                    setAggrgationDetail([]);
                    setCurrentPage(1);
                    setSearchStr("");
                    setFilters([]);
                    setgraphFilters([]);
                    setLabelData([]);
                    setDetails([]);
                    if (view.value === "graph") {
                      refetch();
                    }
                  }}
                  className={`${
                    index !== 0 ? "ml-8" : ""
                  } whitespace-nowrap py-1 px-1 border-b-2 font-medium text-sm ${
                    activeView === view.value
                      ? "border-[#DC3833] text-[#DC3833]"
                      : "border-transparent text-gray-500"
                  }`}
                >
                  {view.label}
                </button>
              ))}

              {/* Filters Button */}
              {removeFilter ? null : (
                <div className="ml-auto relative">
                  <CssTooltip
                    title={
                      labelData.length > 0 ? (
                        <div className="text-xs p-1">
                          {labelData.map((label, idx) => (
                            <div key={idx} className="mb-1">
                              {label}
                            </div>
                          ))}
                        </div>
                      ) : (
                        "No filters applied"
                      )
                    }
                    placement="top"
                    arrow
                  >
                    <button
                      className="whitespace-nowrap rounded-md border border-gray-300 px-4 py-2 font-medium text-sm text-gray-600 hover:border-[#DC3833] hover:text-[#DC3833] relative"
                      onClick={() => {
                        setFilterDialog(true);
                        setGraphFilterDialog(activeView === "graph");
                      }}
                    >
                      {activeView === "table" ? (
                        <span className="flex items-center gap-2">
                          <TableChartIcon className="w-4 h-4" />
                          Table Filter
                        </span>
                      ) : (
                        <span className="flex items-center gap-2">
                          <ChartIcon />
                          Chart Filter
                        </span>
                      )}
                      {badgeCount > 0 && (
                        <div className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full h-5 w-5 flex items-center justify-center text-xs font-medium">
                          {badgeCount}
                        </div>
                      )}
                    </button>
                  </CssTooltip>
                </div>
              )}
            </nav>
          </div>
          {/* ===== TABS END ===== */}
          {/* Report Table  */}

          {/* Show this content only when 'Table View' is active */}
          {activeView === "table" && (
            <>
              {/* Report Table  */}
              {details.length > 0 ? (
                <>
                  {/* Main Report Table */}
                  <div className="mt-5">
                    <ReportTable
                      columns={columns}
                      data={details}
                      //  isLoading={loadingData}
                    />
                  </div>

                  {/* Pagination and Result Count */}
                  <div className="flex items-center justify-between mt-5">
                    <div className="flex items-center">
                      <ResultPerPageComponent
                        countPerPage={resultPerPage}
                        limit={limitPerPage}
                        handleLimitChange={handleLimitChange}
                        pageName="reports"
                      />
                      <div className="text-sm pl-3 text-titleColor">
                        {(currentPage - 1) * limitPerPage + 1} -{" "}
                        {Math.min(limitPerPage * currentPage, totalCount)} of{" "}
                        {totalCount} rows
                      </div>
                    </div>

                    <Pagination
                      className="pagination-bar"
                      currentPage={currentPage}
                      totalCount={totalCount}
                      pageSize={limitPerPage}
                      onPageChange={handlePageChange}
                    />
                  </div>
                </>
              ) : (
                !loadingData && (
                  // Empty State UI
                  <div className="border border-outerBorder mb-5 mt-8">
                    <div className="flex text-headingColor text-2xl justify-center font-bold mt-5">
                      Oops! No records to display.
                    </div>
                    <div className="flex justify-center my-10">
                      <img
                        src={bgImage}
                        className="h-[10%] w-[10%] object-cover"
                        alt="bg"
                      />
                    </div>
                  </div>
                )
              )}
            </>
          )}
          {/* Show this content only when 'Graph View' is active */}
          {activeView === "graph" && showGraph && (
            <>
              <div
                ref={graphContainerRef}
                className="mt-8 text-center text-gray-500 border border-outerBorder py-5"
              >
                {graphFilters?.visualizationType === "line" && showGraph ? (
                  <>
                    <div className="flex justify-end mr-5">
                      <span className="text-xs font-medium text-gray-600">
                        No. of records:{" "}
                        <span className="font-bold text-gray-800">
                          {totalCount >= configApiData.GRAPH_PAGE_LIMIT
                            ? configApiData.GRAPH_PAGE_LIMIT
                            : totalCount}
                          /{totalCount}
                        </span>
                      </span>
                    </div>

                    <LineChartGraph
                      data={details}
                      config={graphFilters}
                      totalCount={totalCount}
                    />
                  </>
                ) : graphFilters?.visualizationType === "bar" ? (
                  <>
                    {" "}
                    <div className="flex justify-end mr-5">
                      <span className="text-xs font-medium text-gray-600">
                        No. of records:{" "}
                        <span className="font-bold text-gray-800">
                          {totalCount >= configApiData.GRAPH_PAGE_LIMIT
                            ? configApiData.GRAPH_PAGE_LIMIT
                            : totalCount}
                          /{totalCount}
                        </span>
                      </span>
                    </div>
                    {/* Bar Chart Download Warning */}
                    <BarChartGraph data={details} config={graphFilters} />
                  </>
                ) : graphFilters?.visualizationType === "pie" ? (
                  <>
                    {" "}
                    <div className="flex justify-end mr-5">
                      <span className="text-xs font-medium text-gray-600">
                        No. of records:{" "}
                        <span className="font-bold text-gray-800">
                          {totalCount >= configApiData.GRAPH_PAGE_LIMIT
                            ? configApiData.GRAPH_PAGE_LIMIT
                            : totalCount}
                          /{totalCount}
                        </span>
                      </span>
                    </div>
                    <PieChartComponent data={details} config={graphFilters} />
                  </>
                ) : graphFilters?.visualizationType === "gauge" ? (
                  <>
                    {" "}
                    <div className="flex justify-end mr-5">
                      <span className="text-xs font-medium text-gray-600">
                        No. of records:{" "}
                        <span className="font-bold text-gray-800">
                          {totalCount >= configApiData.GRAPH_PAGE_LIMIT
                            ? configApiData.GRAPH_PAGE_LIMIT
                            : totalCount}
                          /{totalCount}
                        </span>
                      </span>
                    </div>
                    <GaugeChartGraph data={details} config={graphFilters} />
                  </>
                ) : graphFilters?.visualizationType === "scatter" ? (
                  <>
                    {" "}
                    <div className="flex justify-end mr-5">
                      <span className="text-xs font-medium text-gray-600">
                        No. of records:{" "}
                        <span className="font-bold text-gray-800">
                          {totalCount >= configApiData.GRAPH_PAGE_LIMIT
                            ? configApiData.GRAPH_PAGE_LIMIT
                            : totalCount}
                          /{totalCount}
                        </span>
                      </span>
                    </div>
                    <ScatterPlotChartGraph
                      data={details}
                      config={graphFilters}
                    />
                  </>
                ) : graphFilters?.visualizationType === "heat" ? (
                  <>
                    {" "}
                    <div className="flex justify-end mr-5">
                      <span className="text-xs font-medium text-gray-600">
                        No. of records:{" "}
                        <span className="font-bold text-gray-800">
                          {totalCount >= configApiData.GRAPH_PAGE_LIMIT
                            ? configApiData.GRAPH_PAGE_LIMIT
                            : totalCount}
                          /{totalCount}
                        </span>
                      </span>
                    </div>
                    <HeatMapChartGraph data={details} config={graphFilters} />
                  </>
                ) : graphFilters?.visualizationType === "multiAxis" ? (
                  <>
                    {" "}
                    <div className="flex justify-end mr-5">
                      <span className="text-xs font-medium text-gray-600">
                        No. of records:{" "}
                        <span className="font-bold text-gray-800">
                          {totalCount >= configApiData.GRAPH_PAGE_LIMIT
                            ? configApiData.GRAPH_PAGE_LIMIT
                            : totalCount}
                          /{totalCount}
                        </span>
                      </span>
                    </div>
                    <MultiAxisChartGraph data={details} config={graphFilters} />
                  </>
                ) : (
                  <>
                    <label className="text-xl font-bold text-[#6E6565]">
                      Let’s set up your graph preferences.
                    </label>

                    <img
                      src={graphImage}
                      alt="Graph Representation"
                      className="mx-auto py-5 "
                    />
                    {removeFilter ? null : (
                      <OutlinedButton
                        label="Create Visualization"
                        type="button"
                        buttonClassName={
                          "w-48 border-errorBorder text-errorColor"
                        }
                        onClick={() => {
                          if (!removeFilter) {
                            setFilterDialog(true);
                            setGraphFilterDialog(true);
                          }
                        }}
                      />
                    )}
                  </>
                )}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Export Report Modal */}
      <ExportPopup
        show={showExportConfirmation}
        onHide={() => setShowExportConfirmation(false)}
        onConfirm={(type) => {
          if (activeView === "graph") {
            // Handle graph exports
            if (type.includes("PDF")) {
              exportGraphAsPDF();
            }
            if (type.includes("CSV")) {
              exportGraphDataAsCSV();
            }
          } else {
            // Handle table exports
            exportReport(type);
          }
          setShowExportConfirmation(false);
        }}
        title={"Export Report"}
        identity={"Reports"}
        exportPermissions={
          activeView === "graph"
            ? { csv: true, excel: false, pdf: true } // Only show CSV and PDF for graphs
            : exportPermissions // Use normal permissions for tables
        }
      />

      {/* Alert Modal for Download Permission */}
      <InfoModal
        show={showAlertConfirmation}
        onHide={() => {
          setShowAlertConfirmation(false);
        }}
        message={message}
        graph={activeView === "graph" ? true : false}
        openGraphFilter={() => {
          setFilterDialog(true);
          setGraphFilterDialog(true);
        }}
        applyFilter={() => {
          setShowGraph(true);
        }}
      />
      {/* Send Mail Dialog */}
      <SendMail
        openGroupDialog={sendMailDialog}
        closeGroupDialog={() => {
          setSendMailDialog(false);
        }}
        selectedFilter={selectedFilter}
        searchStr={searchStr}
        type={extensionType}
        reportName={data}
        timeZone={timeZone}
        filters={filters}
        columnConfigData={columnConfigData?.data}
        graphFilters={graphFilters}
        graphValidation={activeView === "graph" ? true : false}
      />

      {/* Toast Notifications */}
      <ToastContainer position="top-center" autoClose={3000} />

      {/* Error and Success Dialogs */}
      <ErrorDialog
        show={errorDialog}
        onHide={() => setErrorDialog(false)}
        message={message}
      />
      <SuccessDialog
        show={successDialog}
        onHide={() => setSuccessDialog(false)}
        message={message}
      />

      {/* Conditional Filter Dialog */}
      {filterDialog && (
        <MetaDataProvider>
          <ReportFilter
            openFilterDialog={filterDialog}
            graphFilter={graphFilterDialog}
            activeView={activeView}
            closeFilterDialog={() => {
              setFilterDialog(false);
            }}
            reportName={data}
            setFilters={setFilters}
            setgraphFilters={setgraphFilters}
            isLoading={isLoading}
            filterData={filters}
            graphFilteredData={graphFilters}
            setLabelData={setLabelData}
            setCurrentPage={setCurrentPage}
            fieldData={columnConfigData}
            configApiData={configApiData}
            totalCount={totalCount}
            onApplyGraphFilter={() => {
              staticReport();
            }}
            panelVisualizationType={"Table Report"}
            graphValidation={activeView === "graph" ? true : false}
          />
        </MetaDataProvider>
      )}
    </>
  );
}

export default StaticReports;
