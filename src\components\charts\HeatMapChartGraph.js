import React from "react";

function HeatMapChartGraph({ data, config }) {
  // Default config
  const defaultConfig = {
    xAxis: "Customer",
    yAxis: "Customer Bind",
    derivedFields: "Count",
    mapColor: "#00FF00",
  };

  const chartConfig = { ...defaultConfig, ...config };

  // Early return if no data
  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <div className="w-full p-6 bg-white">
        <h2 className="text-2xl font-bold mb-6 text-gray-800">
          {chartConfig.xAxis} vs {chartConfig.yAxis} Heat Map
        </h2>
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
          <p className="text-gray-500 text-lg">No data available to display</p>
        </div>
      </div>
    );
  }

  // Get unique customers and customer binds
  const uniqueCustomers = [
    ...new Set(data.map((d) => d[chartConfig.xAxis]).filter(Boolean)),
  ];
  const uniqueCustomerBinds = [
    ...new Set(data.map((d) => d[chartConfig.yAxis]).filter(Boolean)),
  ];

  // Get min and max values for color scaling
  const values = data
    .map((d) => d[chartConfig.derivedFields])
    .filter((v) => v != null);
  const minValue = Math.min(...values);
  const maxValue = Math.max(...values);

  // Color intensity function
  const getColor = (value) => {
    if (value == null || value === 0) return "#fafafa"; // Very light shade for 0 values

    const intensity = (value - minValue) / (maxValue - minValue);

    // Convert hex to RGB for dynamic color calculation
    const hex = chartConfig.mapColor.replace("#", "");
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    // Create different intensity levels using the dynamic color
    if (intensity <= 0.2) {
      return `rgba(${r}, ${g}, ${b}, 0.2)`; // Very light
    } else if (intensity <= 0.4) {
      return `rgba(${r}, ${g}, ${b}, 0.4)`; // Light
    } else if (intensity <= 0.6) {
      return `rgba(${r}, ${g}, ${b}, 0.6)`; // Medium
    } else if (intensity <= 0.8) {
      return `rgba(${r}, ${g}, ${b}, 0.8)`; // Dark
    } else {
      return `rgba(${r}, ${g}, ${b}, 1.0)`; // Very dark
    }
  };

  // Create matrix data
  const matrixData = uniqueCustomers.map((customer) => {
    const row = { customer };
    uniqueCustomerBinds.forEach((bind) => {
      const dataPoint = data.find(
        (d) =>
          d[chartConfig.xAxis] === customer && d[chartConfig.yAxis] === bind
      );
      row[bind] = dataPoint ? dataPoint[chartConfig.derivedFields] : 0;
    });
    return row;
  });

  return (
    <div className="w-full p-6 bg-white">
      {/* Legend */}
      <div className="mb-4 flex items-center gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Low:</span>
          <div
            className="w-4 h-4 border border-gray-300"
            style={{ backgroundColor: getColor(minValue) }}
          />
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">High:</span>
          <div
            className="w-4 h-4 border border-gray-300"
            style={{ backgroundColor: getColor(maxValue) }}
          />
        </div>
        <span className="text-sm text-gray-600">
          Range: {minValue} - {maxValue}
        </span>
      </div>

      {/* Heatmap Table */}
      <div
        className="overflow-auto border rounded-lg shadow-sm"
        style={{ maxHeight: "800px" }}
      >
        <table className="border-collapse bg-white">
          <thead>
            <tr>
              <th
                className="border border-gray-300 p-3 text-left text-xs font-semibold text-gray-700 sticky left-0 z-10 min-w-[200px]"
                style={{ backgroundColor: "#E7F1FD" }}
              >
                {/* {chartConfig.xAxis} */}
              </th>
              {uniqueCustomerBinds.map((bind) => (
                <th
                  key={bind}
                  className="border border-gray-300 p-2 text-center text-xs font-semibold text-gray-700 min-w-[150px]"
                  style={{
                    //writingMode: "vertical-rl",
                    textOrientation: "mixed",
                    backgroundColor: "#E7F1FD",
                  }}
                  title={bind}
                >
                  <div className="h-12 flex items-center justify-center">
                    {bind.length > 15 ? bind.substring(0, 12) + "..." : bind}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {matrixData.map((row, rowIndex) => (
              <tr key={rowIndex} className="hover:bg-gray-50">
                <td
                  className="border border-gray-300 p-3 text-xs font-medium text-gray-800 sticky left-0 z-10 min-w-[100px]"
                  style={{ backgroundColor: "#E7F1FD" }}
                >
                  <div className="truncate" title={row.customer}>
                    {row.customer}
                  </div>
                </td>
                {uniqueCustomerBinds.map((bind) => (
                  <td
                    key={bind}
                    className="border border-gray-300 text-center text-xs font-medium text-gray-800 min-w-[100px] h-12"
                    style={{
                      backgroundColor: getColor(row[bind]),
                      position: "relative",
                    }}
                    title={`${row.customer} - ${bind}: ${
                      row[bind] !== null && row[bind] !== undefined
                        ? row[bind]
                        : "0"
                    }`}
                  >
                    <div className="flex items-center justify-center h-full">
                      {row[bind] !== null && row[bind] !== undefined
                        ? row[bind]
                        : "0"}
                    </div>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default HeatMapChartGraph;
