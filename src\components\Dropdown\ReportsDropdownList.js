import React, { useState, useRef, useEffect, memo } from "react";
import ExpandMore from "@mui/icons-material/ExpandMore";
import ExpandLess from "@mui/icons-material/ExpandLess";
import SearchIcon from "@mui/icons-material/Search";
import ClickAwayListener from "@mui/material/ClickAwayListener";
import { useTranslation } from "react-i18next";
import { CircularProgress } from "@mui/material";
import { VariableSizeList as List, areEqual } from "react-window";
import { CloseIcon } from "../../icons";
import theme from "../../tailwind-theme";
import { CssTooltip } from "../StyledComponent";

const ReportsDropdownList = ({
  btnName,
  btnWidth,
  btnHeight,
  width,
  data,
  isLoading,
  reset,
  onSelectionChange,
  isMulti = true,
  disabled = false,
  isSearch = true,
  optionDataList,
  onBlur,
  value,
  defaultSelectedData,
}) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState(
    Array.isArray(value) ? value : []
  );
  const [searchText, setSearchText] = useState("");
  const [inputValue, setInputValue] = useState("");
  const buttonRef = useRef(null);

  const Styles = {
    btnPrimary: `bg-transparent text-gray-400 px-2 w-full border border-outerBorder flex min-h-[40px] items-center rounded-[5px] ${
      btnWidth ? btnWidth : "min-w-[400px]"
    } ${btnHeight ? btnHeight : "h-full"}`,
  };
  const itemHeight = 30;
  const maxListHeight = 150;

  const handleOpen = (e) => {
    e.preventDefault();
    setOpen(!open);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleSelectAll = () => {
    setSelectedOptions((prevSelectedOptions) => {
      if (
        (Array.isArray(prevSelectedOptions)
          ? prevSelectedOptions
          : []
        ).includes("Select All")
      ) {
        return [];
      } else {
        let optionsToSelect = data
          .filter((item) => item.value !== "Select All")
          .slice(0, optionDataList || data.length)
          .map((item) => item.value);

        return [...optionsToSelect, "Select All"];
      }
    });
  };

  const handleCheckboxChange = (option) => {
    if (option.value === "Select All") {
      handleSelectAll();
    } else {
      setSelectedOptions((prevSelectedOptions) => {
        let updatedOptions;
        if (!isMulti) {
          updatedOptions = [option.value];
        } else {
          const prevSelectedArray = Array.isArray(prevSelectedOptions)
            ? prevSelectedOptions
            : [];
          if (optionDataList) {
            if (prevSelectedArray.length === data.length - 1) {
              updatedOptions = [
                ...prevSelectedArray,
                option.value,
                "Select All",
              ];
            } else {
              updatedOptions = prevSelectedArray.includes(option.value)
                ? prevSelectedArray.filter(
                    (selectedOption) => selectedOption !== option.value
                  )
                : prevSelectedArray.length >= optionDataList
                ? [...prevSelectedArray.slice(1), option.value]
                : [...prevSelectedArray, option.value];
            }
          } else {
            updatedOptions = prevSelectedArray.includes(option.value)
              ? prevSelectedArray.filter(
                  (selectedOption) => selectedOption !== option.value
                )
              : [...prevSelectedArray, option.value];
          }
        }
        const allOtherOptionsSelected = data
          .map((element) => element.value)
          .filter((value) => value !== "Select All")
          .every((value) => updatedOptions.includes(value));

        if (allOtherOptionsSelected) {
          updatedOptions.push("Select All");
        } else {
          updatedOptions = updatedOptions.filter(
            (selectedOption) => selectedOption !== "Select All"
          );
        }

        return updatedOptions;
      });
    }
  };

  const handleSearchChange = (event) => {
    setSearchText(event.target.value);
  };

  useEffect(() => {
    setSelectedOptions([]);
    setSearchText("");
  }, [reset]);

  useEffect(() => {
    onSelectionChange(Array.isArray(selectedOptions) ? selectedOptions : []);
    setInputValue(
      (Array.isArray(selectedOptions) ? selectedOptions : []).join(", ")
    );
  }, [selectedOptions]);

  useEffect(() => {
    if (Array.isArray(value)) {
      let updatedSelectedOptions = [...value];
      const allOptionsExceptSelectAll = data
        .map((element) => element.value)
        .filter((val) => val !== "Select All");
      const allSelected =
        allOptionsExceptSelectAll.length > 0 &&
        allOptionsExceptSelectAll.every((val) =>
          updatedSelectedOptions.includes(val)
        );
      if (allSelected && !updatedSelectedOptions.includes("Select All")) {
        updatedSelectedOptions.push("Select All");
      }
      if (
        updatedSelectedOptions.length !== selectedOptions.length ||
        updatedSelectedOptions.some((val) => !selectedOptions.includes(val))
      ) {
        setSelectedOptions(updatedSelectedOptions);
        setInputValue(updatedSelectedOptions.join(", "));
      }
    }
  }, [value]);

  const filteredOptions =
    data?.length > 0
      ? data.filter((option) =>
          String(option.label).toLowerCase().includes(searchText.toLowerCase())
        )
      : [];

  const removeSelectedOption = (e, optionValue) => {
    e.preventDefault();
    e.stopPropagation();
    setSelectedOptions((prevSelectedOptions) => {
      const prevSelectedArray = Array.isArray(prevSelectedOptions)
        ? prevSelectedOptions
        : [];
      const updatedOptions = prevSelectedArray.filter(
        (selectedOption) => selectedOption !== optionValue
      );
      if (updatedOptions.includes("Select All")) {
        return updatedOptions.filter(
          (selectedOption) => selectedOption !== "Select All"
        );
      }
      return updatedOptions;
    });
  };

  let combinedOptions = [
    ...(Array.isArray(selectedOptions) ? selectedOptions : [])
      .map((selectedOption) =>
        data?.find((option) => option.value === selectedOption)
      )
      .filter(Boolean),
    ...filteredOptions.filter((option) => {
      return (
        option &&
        option?.value &&
        option?.label &&
        !(Array.isArray(selectedOptions) ? selectedOptions : []).includes(
          option.value
        )
      );
    }),
  ];

  const selectAllIndex = combinedOptions.findIndex(
    (option) => option.value === "Select All"
  );

  if (selectAllIndex !== -1) {
    const selectAllOption = combinedOptions.splice(selectAllIndex, 1)[0];
    combinedOptions = [selectAllOption, ...combinedOptions];
  }

  const truncate = (text, maxLength = 10) => {
    return text?.length > maxLength ? text.slice(0, maxLength) + "..." : text;
  };

  useEffect(() => {
    if (defaultSelectedData) {
      setSelectedOptions(defaultSelectedData);
    }
  }, []);

  const Row = memo(({ data, index, style }) => {
    const currentOption = combinedOptions?.[index];
    return (
      <div key={index} style={style} className="mb-4">
        <label className="flex justify-between items-center gap-2 text-xs">
          {currentOption?.label?.length > 20 ? (
            <CssTooltip title={currentOption.label}>
              <div className="text-xs truncate max-w-[180px]">
                {truncate(currentOption.label)}
              </div>
            </CssTooltip>
          ) : (
            <div className="text-xs truncate max-w-[180px]">
              {currentOption?.label}
            </div>
          )}

          <div className="flex-grow flex justify-end mr-3">
            <input
              type="checkbox"
              checked={
                currentOption?.value === "Select All"
                  ? data
                      .map((element) => element.value)
                      .filter((val) => val !== "Select All")
                      .every((val) =>
                        (Array.isArray(selectedOptions)
                          ? selectedOptions
                          : []
                        ).includes(val)
                      )
                  : (Array.isArray(selectedOptions)
                      ? selectedOptions
                      : []
                    ).includes(currentOption?.value)
              }
              onChange={() => handleCheckboxChange(currentOption)}
              className="min-w-[20px] w-4 h-4"
              style={{
                accentColor: `${theme?.backgroundColor.bgCheckboxSelection}`,
              }}
            />
          </div>
        </label>
      </div>
    );
  }, areEqual);

  return (
    <ClickAwayListener onClickAway={handleClose}>
      <div
        className={`${btnWidth ? "" : "min-w-[150px]"} relative text-medium`}
      >
        <button
          ref={buttonRef}
          onClick={handleOpen}
          onBlur={onBlur}
          className={
            open
              ? `flex ${Styles.btnPrimary} border-outerBorder`
              : Styles.btnPrimary
          }
          disabled={disabled}
        >
          <div className="flex w-11/12 flex-wrap">
            <div className="text-xs mt-1 flex flex-wrap">
              {(Array.isArray(selectedOptions) ? selectedOptions : []).length >
              0 ? (
                <CssTooltip
                  title={
                    <div className="max-h-[150px] overflow-y-auto max-w-[250px]">
                      {(Array.isArray(selectedOptions)
                        ? selectedOptions
                        : []
                      ).map((selectedValue, index) => {
                        const option = combinedOptions.find(
                          (opt) => opt.value === selectedValue
                        );
                        return (
                          option &&
                          option.label !== "Select All" && (
                            <div key={index} className="text-xs mb-2">
                              {option.label}
                            </div>
                          )
                        );
                      })}
                    </div>
                  }
                  arrow
                  placement="top"
                >
                  <div className="flex whitespace-nowrap items-center cursor-pointer">
                    {(() => {
                      const selectedArray = Array.isArray(selectedOptions)
                        ? selectedOptions
                        : [];
                      const filteredOptions = selectedArray.filter((val) => {
                        const opt = combinedOptions.find(
                          (opt) => opt.value === val
                        );
                        return opt && opt.label !== "Select All";
                      });

                      const firstOption = filteredOptions[0];
                      const firstOptionData = combinedOptions.find(
                        (opt) => opt.value === firstOption
                      );
                      const remainingCount = filteredOptions.length - 1;

                      return (
                        <>
                          {firstOptionData && (
                            <span className="flex text-black items-center m-1 rounded h-6 px-2 bg-bgTeritary">
                              <span>{truncate(firstOptionData.label)}</span>
                              <CloseIcon
                                onClick={(e) =>
                                  removeSelectedOption(e, firstOptionData.value)
                                }
                                className="h-1.5 ml-1"
                              />
                            </span>
                          )}
                          {remainingCount > 0 && (
                            <span
                              className={`text-gray-500 mt-1 ${
                                remainingCount > 1000 ? "text-[9px]" : ""
                              }`}
                            >
                              +{remainingCount}
                            </span>
                          )}
                        </>
                      );
                    })()}
                  </div>
                </CssTooltip>
              ) : btnName ? (
                btnName
              ) : (
                ""
              )}
            </div>
          </div>
          <div>
            {open ? (
              <ExpandLess className="ml-2" />
            ) : (
              <ExpandMore className="ml-2" />
            )}
          </div>
        </button>

        {open && (
          <div className="relative bottom-0 left-0 z-50">
            <div
              className={`bg-white shadow-md p-4 rounded absolute ${
                btnWidth ? "min-w-full" : "min-w-[150px]"
              }`}
            >
              {isSearch && (
                <div className="mb-4">
                  <div className="flex items-center border border-gray-300 px-2 py-1 w-full">
                    <SearchIcon className="text-gray-400 mr-4 ml-1" />
                    <input
                      type="text"
                      placeholder="Search"
                      value={searchText}
                      onChange={handleSearchChange}
                      className="border-none outline-none w-full text-xs"
                    />
                  </div>
                </div>
              )}

              {isLoading ? (
                <div className="flex justify-center">
                  <CircularProgress />
                </div>
              ) : (
                <div className="w-full max-h-64">
                  <List
                    width={width}
                    height={Math.min(
                      combinedOptions.length * itemHeight,
                      maxListHeight
                    )}
                    itemCount={combinedOptions.length}
                    itemData={combinedOptions}
                    itemSize={(index) =>
                      combinedOptions[index]?.label?.length > 150 ? 75 : 30
                    }
                  >
                    {Row}
                  </List>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </ClickAwayListener>
  );
};

export default ReportsDropdownList;
